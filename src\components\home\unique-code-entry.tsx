'use client';

import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { HomepageAnnouncement } from '@/components/home/<USER>';
import { Button } from '@/components/ui/button';
import { CountdownInput } from '@/components/ui/countdown-input';
import { Label } from '@/components/ui/label';
import { useCensusAuth } from '@/hooks/useCensusAuth';
import { useMessage } from '@/hooks/useMessage';
import type { CensusData } from '@/lib/homepage/placeholder-processor';
import { zodResolver } from '@/lib/utils/zod-resolver-compat';
import {
  type ClientCensusCodeFormValues,
  createClientCensusCodeSchema,
} from '@/lib/validation/client/census-client';

interface UniqueCodeEntryProps {
  censusData?: CensusData;
  locale?: string;
}

export function UniqueCodeEntry({
  censusData,
  locale = 'en-AU',
}: UniqueCodeEntryProps = {}) {
  const searchParams = useSearchParams();
  const { showError } = useMessage();
  const { signInWithCode, isSubmitting, rateLimit } = useCensusAuth();

  // Translation hooks
  const t = useTranslations('census');
  const tAuth = useTranslations('auth');
  const tCommon = useTranslations('common');
  const tNav = useTranslations('navigation');
  const tValidation = useTranslations('validation');
  const tLegal = useTranslations('legal');

  // Create translated schema
  const translatedSchema = createClientCensusCodeSchema(tValidation);

  const form = useForm<ClientCensusCodeFormValues>({
    resolver: zodResolver(translatedSchema),
    defaultValues: {
      code: '',
    },
  });

  // Auto-populate the code from URL query parameter on component mount
  useEffect(() => {
    // Get the code from the URL query parameter if searchParams exists
    if (searchParams) {
      const codeFromUrl = searchParams.get('code');

      // If code exists in URL, set it in the form
      if (codeFromUrl) {
        form.setValue('code', codeFromUrl);
      }
    }
  }, [searchParams, form]);

  // Handle form submission
  const onSubmit = async (data: ClientCensusCodeFormValues) => {
    // Don't submit if locked
    if (rateLimit.isLocked) {
      return;
    }

    try {
      // Strategic server validation before authentication attempt (hybrid approach)
      let currentStatus;
      try {
        currentStatus = await rateLimit.checkStatus();
      } catch (statusError) {
        // If status check fails, don't proceed with authentication for security
        if (process.env.NODE_ENV === 'development') {
          console.warn(
            'Rate limit status check failed, blocking authentication attempt:',
            statusError
          );
        }
        showError('serverError', 'auth');
        return;
      }

      // Use the returned status to avoid async race conditions
      if (currentStatus.isLocked) {
        return;
      }

      // Ensure we're using the census auth system
      if (process.env.NODE_ENV === 'development') {
        console.log('Home page: Using census auth system for login');
      }

      // Use the census auth hook to sign in with the code
      await signInWithCode(data.code);
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Census authentication error:', error);
      }
      showError('authenticationError', 'auth');
    }
  };

  // Handle manual submit (for countdown input)
  const handleSubmit = () => {
    if (!(rateLimit.isLocked || isSubmitting)) {
      form.handleSubmit(onSubmit)();
    }
  };

  return (
    <div className="mx-auto w-full max-w-sm">
      <div className="flex flex-col gap-5 p-4 md:p-0">
        <div className="flex flex-col items-center space-y-1 text-center">
          <h1 className="font-bold text-2xl">{t('welcome')}</h1>

          {/* Inline announcement between title and subheading */}
          {censusData && (
            <HomepageAnnouncement censusData={censusData} locale={locale} />
          )}

          <p className="mt-1 text-balance text-muted-foreground text-sm">
            {t('enterDetailsBelow')}
          </p>
        </div>
        <div className="grid gap-5">
          <form className="space-y-3" onSubmit={form.handleSubmit(onSubmit)}>
            <div className="space-y-2">
              <Label htmlFor="code">{tAuth('censusCode')}</Label>
              <CountdownInput
                disabled={isSubmitting}
                error={!!form.formState.errors.code}
                isLocked={rateLimit.isLocked}
                onChange={(value) => form.setValue('code', value)}
                onQrCodeScanned={(code) => {
                  form.setValue('code', code);
                  // Clear any existing errors when QR code is scanned
                  form.clearErrors('code');
                }}
                onSubmit={handleSubmit}
                placeholder={tAuth('censusCode')}
                remainingTime={rateLimit.formattedTime}
                showQrScanner={true}
                value={form.watch('code')}
              />
              {form.formState.errors.code && (
                <p className="text-destructive text-sm">
                  {form.formState.errors.code.message}
                </p>
              )}
            </div>
            <Button
              className="w-full"
              disabled={rateLimit.isLocked || isSubmitting}
              type="submit"
            >
              {isSubmitting ? tCommon('validating') : tCommon('startCensus')}
            </Button>
          </form>
          <div className="relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-border after:border-t">
            <span className="relative z-10 bg-background px-2 text-muted-foreground text-xs">
              {tLegal('termsAndConditions')}
            </span>
          </div>
          <div className="text-center text-muted-foreground text-xs">
            {tLegal('byClickingContinue')}{' '}
            <Link className="text-primary hover:underline" href="/terms">
              {tLegal('termsOfService')}
            </Link>{' '}
            {tLegal('and')}{' '}
            <Link
              className="text-primary hover:underline"
              href="/privacy-policy"
            >
              {tLegal('privacyPolicy')}
            </Link>
            .
          </div>
          <div className="text-center text-sm">
            <button
              className="text-muted-foreground underline underline-offset-4 hover:text-primary cursor-pointer"
              onClick={() => window.location.href = '/admin/login'}
              type="button"
            >
              {tNav('adminLogin')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
