'use client';

import { <PERSON><PERSON>Circle, Trash2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useCallback, useState } from 'react';
import { useForm } from 'react-hook-form';
import { FormField } from '@/components/forms/common/FormField';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Drawer,
  DrawerContent,
} from '@/components/ui/drawer';
import { useIsMobile } from '@/hooks/use-mobile';
import { useCensusAuth } from '@/hooks/useCensusAuth';
import { useMessage } from '@/hooks/useMessage';
import { clearCensusLocalStorage } from '@/lib/utils/census-storage-cleanup';
import { zodResolver } from '@/lib/utils/zod-resolver-compat';
import { createClientDeleteAccountVerificationSchema } from '@/lib/validation/client/census-client';

/**
 * DeleteAccountButton component
 *
 * Provides a button that opens a confirmation dialogue for account deletion.
 * On confirmation, it calls the delete-account API endpoint and handles the response.
 *
 * Features:
 * - Responsive design: Dialog for desktop, Drawer for mobile
 * - Native swipe-to-dismiss on mobile with visual handle bar
 * - Hidden scrollbar for clean mobile appearance
 * - Confirmation dialogue with clear explanation of consequences
 * - Requires typing "DELETE NOW" to confirm deletion
 * - Loading state during deletion process
 * - Success/error toast notifications
 * - Automatic sign out and redirect after successful deletion
 */
export function DeleteAccountButton() {
  const [isOpen, setIsOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const router = useRouter();
  const { signOutFromCensus } = useCensusAuth();
  const { showSuccess, showError } = useMessage();
  const isMobile = useIsMobile();

  // Translation hooks
  const t = useTranslations('dialogs');
  const tCommon = useTranslations('common');
  const tValidation = useTranslations('validation');
  const tErrors = useTranslations('errors');

  // Create translated schema
  const translatedSchema =
    createClientDeleteAccountVerificationSchema(tValidation);

  // Initialize form with validation
  const form = useForm({
    resolver: zodResolver(translatedSchema),
    defaultValues: {
      confirmationPhrase: '',
    },
  });

  /**
   * Handles the account deletion process
   * - Validates the confirmation phrase
   * - Calls the delete-account API endpoint
   * - Shows success/error messages
   * - Signs out and redirects on success
   */
  const handleDeleteAccount = useCallback(
    async (data: { confirmationPhrase: string }) => {
      setIsDeleting(true);
      try {
        const response = await fetch('/api/census/delete-account', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            confirmationPhrase: data.confirmationPhrase,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || tErrors('AccountDeleteFailed'));
        }

        // Clear all census-related localStorage data for re-registration scenarios
        clearCensusLocalStorage();

        // Sign out and redirect to home page
        // Success message will be shown on homepage via census_toast cookie
        await signOutFromCensus();
        router.push('/');
      } catch (error) {
        showError('AccountDeleteFailed');
        setIsOpen(false);
      } finally {
        setIsDeleting(false);
      }
    },
    [showError, signOutFromCensus, router, tErrors]
  );

  // Reset form when dialogue opens/closes
  const handleOpenChange = useCallback(
    (open: boolean) => {
      setIsOpen(open);
      if (!open) {
        form.reset(); // Clear form when dialogue closes
      }
    },
    [form]
  );

  // Performance Optimization: Memoized content component
  const DialogContentComponent = useCallback(
    () => (
      <>
        <div className="my-2 rounded-md bg-destructive/10 p-4">
          <p className="font-medium text-destructive text-sm">
            {t('whatWillBeDeleted')}
          </p>
          <ul className="mt-2 list-inside list-disc space-y-1 text-destructive/90 text-sm">
            <li>{t('yourUniqueCode')}</li>
            <li>{t('yourHouseholdInformation')}</li>
            <li>{t('allMemberDetails')}</li>
            <li>{t('anySacramentRecords')}</li>
          </ul>
        </div>

        <div className="mt-4 space-y-4">
          <FormField
            error={form.formState.errors.confirmationPhrase}
            id="confirmationPhrase"
            label={t('typeDeleteNow')}
            placeholder={t('deleteNowPlaceholder')}
            register={form.register}
            required
            variant="line"
          />
        </div>
      </>
    ),
    [t, form]
  );

  // Performance Optimization: Memoized action buttons component
  const ActionButtons = useCallback(() => {
    const handleDeleteClick = async () => {
      const isValid = await form.trigger(); // Validate form
      if (isValid) {
        const data = form.getValues();
        await handleDeleteAccount(data);
      }
    };

    return (
      <div className="flex justify-end gap-2">
        <Button
          className="cursor-pointer"
          disabled={isDeleting}
          onClick={() => handleOpenChange(false)}
          type="button"
          variant="outline"
        >
          {tCommon('cancel')}
        </Button>
        <Button
          className="cursor-pointer"
          disabled={isDeleting}
          onClick={handleDeleteClick}
          type="button"
          variant="destructive"
        >
          {isDeleting ? (
            <>
              <div
                aria-hidden="true"
                className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent"
              />
              {tCommon('processing')}
            </>
          ) : (
            t('deleteAccount')
          )}
        </Button>
      </div>
    );
  }, [isDeleting, form, handleDeleteAccount, handleOpenChange, tCommon, t]);

  // Mobile implementation using Drawer (native swipe-to-dismiss from bottom)
  // Features: Visual handle bar, smooth swipe gestures, and hidden scrollbar
  if (isMobile) {
    return (
      <>
        <Button
          className="cursor-pointer"
          onClick={() => setIsOpen(true)}
          variant="destructive"
        >
          <Trash2 className="mr-2 h-4 w-4" />
          {t('deleteAccount')}
        </Button>

        <Drawer onOpenChange={handleOpenChange} open={isOpen}>
          <DrawerContent className="flex max-h-[80vh] min-h-[60vh] flex-col">
            <div className="scrollbar-hide flex-1 overflow-y-auto px-4 pb-4">
              <div className="pb-4 pt-4 text-left">
                <h2 className="flex items-center font-semibold text-lg">
                  <AlertCircle className="mr-2 h-5 w-5 text-destructive" />
                  {t('deleteAccount')}
                </h2>
                <p className="text-muted-foreground text-sm">
                  {t('thisActionCannotBeUndone')}
                </p>
              </div>

              <div className="space-y-6">
                <DialogContentComponent />
              </div>

              <div className="pt-4 mt-6">
                <ActionButtons />
              </div>
            </div>
          </DrawerContent>
        </Drawer>
      </>
    );
  }

  // Desktop implementation using Dialog
  return (
    <>
      <Button
        className="cursor-pointer"
        onClick={() => setIsOpen(true)}
        variant="destructive"
      >
        <Trash2 className="mr-2 h-4 w-4" />
        {t('deleteAccount')}
      </Button>

      <Dialog onOpenChange={handleOpenChange} open={isOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <AlertCircle className="mr-2 h-5 w-5 text-destructive" />
              {t('deleteAccount')}
            </DialogTitle>
            <DialogDescription>
              {t('thisActionCannotBeUndone')}
            </DialogDescription>
          </DialogHeader>

          <DialogContentComponent />

          <DialogFooter className="flex flex-col-reverse pt-2 sm:flex-row sm:justify-end sm:space-x-2">
            <ActionButtons />
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
