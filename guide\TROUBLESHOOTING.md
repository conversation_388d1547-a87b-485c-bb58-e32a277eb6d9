# WSCCC Census System Troubleshooting Guide

## Common Issues and Solutions

### Authentication Issues

#### SSR Session Data Staleness

**Problem**: UI components not reflecting updated session data immediately after authentication changes

**Symptoms**:
- Progress tracker not appearing after household registration completion
- User information not updating in header after profile changes
- Authorization-dependent UI elements showing stale state
- Requires manual page refresh to see updated session data

**Root Cause**: Server components using cached session data from `getServerSession()`

**Solution**: Add `force-dynamic` to layouts requiring fresh session data

```typescript
// app/[locale]/census/layout.tsx
export const dynamic = 'force-dynamic';

export default async function CensusLayout() {
  // This now gets fresh session data every request
  const session = await getServerSession(censusAuthOptions);

  // Authorization decisions based on current session state
  const isSecondStage = isAuthenticated && !!session?.user?.householdId;

  return <CensusHeader isSecondStage={isSecondStage} />;
}
```

**When to Apply**:
- ✅ Layouts with session-dependent authorization logic
- ✅ Components controlling UI visibility based on user state
- ✅ Security-critical server components
- ❌ Static pages with no session dependencies
- ❌ High-traffic pages where caching is essential

**Verification**:
1. Complete household registration
2. Progress tracker should appear immediately
3. No page refresh required
4. Check browser DevTools for fresh session data

#### NextAuth Cross-Context Navigation Issues

**Problem**: CLIENT_FETCH_ERROR when navigating between admin and census portals

**Symptoms**:
- Error: `"[object Object]" is not valid JSON`
- NextAuth CLIENT_FETCH_ERROR in browser console
- Error: `JSON.parse: unexpected character at line 1 column 2`
- Occurs when:
  - Admin users navigate to homepage after 8+ hours
  - Census users navigate to admin login page
  - Cross-context navigation with expired sessions

**Root Cause**: Client-side navigation (`<Link>`) between different authentication contexts causes session cookie conflicts

**Solution**: Implemented **Defense-in-Depth** approach with two complementary layers:

**Layer 1: Pathname-Based Session Provider Selection**
```typescript
// src/providers/combined-auth-provider.tsx
const renderAuthProvider = () => {
  const isAdminPath = pathname?.startsWith('/admin');

  if (isAdminPath) {
    return <SessionProvider basePath="/api/auth">{children}</SessionProvider>;
  }

  return (
    <SessionProvider
      basePath="/api/census/auth"
      refetchOnWindowFocus={false}
      refetchInterval={0}
    >
      {children}
    </SessionProvider>
  );
};
```

**Layer 2: Full Page Navigation for Cross-Context Links**
```typescript
// Cross-context navigation uses full page refresh
<button onClick={() => window.location.href = '/admin/login'}>
  Admin Login
</button>

<button onClick={() => window.location.href = '/'}>
  Homepage
</button>
```

**Fixed Components**:
- `src/components/admin/user-dropdown.tsx` - Homepage link
- `src/components/home/<USER>
- `src/components/home/<USER>
- `src/components/home/<USER>
- `app/[locale]/admin/login/login-page.tsx` - Homepage links

**Why This Works**:
- **Pathname solution**: Ensures correct auth provider for each page
- **Full page navigation**: Prevents client-side session conflicts
- **Industry standard**: Same approach used by major platforms (Shopify, WordPress, etc.)

**Prevention**: Clean pathname-based logic eliminates race conditions, manual overrides, and state management complexity. Each route deterministically uses the correct auth provider.

#### Census Control Settings Not Taking Effect Immediately

**Problem**: Admin closes census portal but homepage still shows census form until page refresh

**Symptoms**:
- Admin updates census controls (open/close) in admin portal
- Homepage continues showing UniqueCodeEntry instead of CensusClosedMessage
- Changes only take effect after manual page refresh or waiting 60+ seconds
- Admin census status indicator updates correctly but public homepage doesn't

**Root Cause**: Census availability check uses caching with 60-second TTL, causing stale data on homepage

**Solution**: Reduced cache TTL and improved cache invalidation

```typescript
// src/lib/census/census-availability.ts
const CENSUS_STATUS_CACHE_TTL = 10 * 1000; // Reduced from 60 to 10 seconds

export function clearCensusStatusCache(): void {
  // Clear all census-related cache keys
  appCache.delete(CENSUS_STATUS_CACHE_KEY);
  appCache.delete(CENSUS_SETTINGS_CACHE_KEY);

  // Clear any other census-related cache keys
  const allKeys = appCache.getAllKeys();
  allKeys.forEach(key => {
    if (key.startsWith('census:')) {
      appCache.delete(key);
    }
  });
}

// API routes with force-dynamic to prevent Next.js caching
export const dynamic = 'force-dynamic';
```

**Prevention**: Short cache TTL (10 seconds) ensures rapid updates while maintaining performance. Comprehensive cache clearing removes all census-related cached data when admin makes changes.

#### NextAuth URL Redirection Problems

**Problem**: Redirects to localhost:3000 instead of proper domain in production

**Symptoms**:
- Works correctly on Vercel deployment
- Fails in local production builds (`npm run build` + `npm start`)
- Redirects to localhost:3000 regardless of actual domain

**Solutions**:

1. **Set NEXTAUTH_URL correctly**:
   ```bash
   # For HTTPS production
   NEXTAUTH_URL=https://your-domain.com
   
   # For HTTP testing (not recommended for production)
   NEXTAUTH_URL=http://your-domain.com
   ```

2. **Use dertin solution for production builds**:
   ```bash
   env | grep -e NEXTAUTH_URL >> .env.production
   npm run build
   ```

3. **Configure cookie security for HTTP testing**:
   ```typescript
   cookies: {
     sessionToken: {
       options: {
         secure: process.env.NODE_ENV === 'production' && process.env.NEXTAUTH_URL?.startsWith('https'),
       },
     },
   }
   ```

#### NextAuth.js CLIENT_FETCH_ERROR

**Problem**: Console error `[next-auth][error][CLIENT_FETCH_ERROR]` when navigating between admin and census systems

**Root Cause**: Client-side navigation between different authentication contexts causes session cookie conflicts

**Current Solution**: **Defense-in-Depth** approach with multiple protection layers:

**Layer 1: Pathname-Based Session Provider Selection**
- Automatically selects correct SessionProvider based on current route
- Admin paths (`/admin/*`) use admin provider
- All other paths use census provider
- Prevents wrong auth provider from being used

**Layer 2: Full Page Navigation for Cross-Context Links**
- Replaces client-side `<Link>` with `window.location.href` for cross-context navigation
- Forces complete page refresh and clean session handling
- Eliminates session cookie conflicts during navigation
- Industry standard approach for cross-authentication contexts

**Layer 3: Middleware Protection (Fallback)**
- Blocks cross-auth API calls as secondary defense
- Returns structured error responses for debugging
- Provides additional security layer

**Troubleshooting Steps**:
1. **Verify cross-context links use full page navigation**:
   ```typescript
   // ✅ Correct - Full page navigation
   <button onClick={() => window.location.href = '/admin/login'}>

   // ❌ Incorrect - Client-side navigation
   <Link href="/admin/login">
   ```

2. **Check browser DevTools for CLIENT_FETCH_ERROR**:
   - Should not occur with current implementation
   - If present, indicates missed cross-context link

3. **Verify pathname-based provider selection**:
   ```typescript
   // CombinedAuthProvider should select correct provider
   const isAdminPath = pathname?.startsWith('/admin');
   ```

4. **Clear browser cookies** if issues persist:
   - Admin: `admin-session-token`
   - Census: `census-session-token`

**Fixed Components** (using full page navigation):
- Admin dropdown homepage link
- Census portal admin login links
- Admin login page homepage links
- All cross-context navigation buttons

#### Two-Factor Authentication Issues

**Problem**: 2FA QR code not displaying or TOTP validation failing

**Solutions**:
1. **Check QR code generation**:
   ```typescript
   // Verify QR code cache is working
   const qrCode = await generateQRCode(totpUri);
   ```

2. **Validate TOTP secret format**:
   ```typescript
   // Ensure secret is base32 encoded
   const secret = authenticator.generateSecret();
   ```

3. **Check time synchronization**:
   - Ensure server time is synchronised with NTP
   - Verify client device time is accurate

### Database Issues

#### Connection Pool Exhaustion

**Problem**: "Connection pool exhausted" errors

**Symptoms**:
- Intermittent database connection failures
- Slow response times
- Connection timeout errors

**Solutions**:

1. **Check connection pool configuration**:
   ```typescript
   // Verify pool settings in prisma client
   const prisma = new PrismaClient({
     datasources: {
       db: {
         url: process.env.DATABASE_URL,
       },
     },
   });
   ```

2. **Monitor active connections**:
   ```sql
   -- PostgreSQL: Check active connections
   SELECT count(*) FROM pg_stat_activity;
   ```

3. **Optimise query patterns**:
   ```typescript
   // Use connection pooling efficiently
   const result = await prisma.$transaction(async (tx) => {
     // Multiple operations in single transaction
   });
   ```

#### Migration Issues

**Problem**: Prisma migrations failing

**Solutions**:
1. **Reset database schema**:
   ```bash
   npx prisma migrate reset
   npx prisma db push
   ```

2. **Manual migration**:
   ```bash
   npx prisma migrate resolve --applied "migration_name"
   ```

3. **Check database permissions**:
   ```sql
   -- Ensure user has proper permissions
   GRANT ALL PRIVILEGES ON DATABASE your_db TO your_user;
   ```

### Rate Limiting Issues

#### Users Getting Locked Out Unexpectedly

**Problem**: Legitimate users experiencing lockouts

**Solutions**:

1. **Check rate limit configuration**:
   ```typescript
   const RATE_LIMIT_CONFIG = {
     MAX_ATTEMPTS: 5,
     BASE_LOCKOUT_MINUTES: 15,
     ESCALATION_INCREMENT_MINUTES: 15,
   };
   ```

2. **Reset rate limit for specific user**:
   ```typescript
   await prisma.authRateLimit.delete({
     where: { sessionToken: hashedToken }
   });
   ```

3. **Monitor rate limit logs**:
   ```sql
   SELECT * FROM auth_rate_limits 
   WHERE lockout_until > NOW() 
   ORDER BY created_at DESC;
   ```

### AI Analytics Chatbot Issues

#### Getting JSON Response Instead of Processed Answer

**Problem**: Raw JSON displayed in chat instead of natural language response

**Symptoms**:
```json
{
  "shouldExecuteQuery": true,
  "databaseQuery": {
    "query": "SELECT COUNT(*) AS total_members FROM members;",
    "params": []
  }
}
```

**Root Causes**:
1. Gemini API returns mixed text + JSON
2. JSON extraction fails on backend
3. Database query execution fails

**Solutions**:

1. **Check API key configuration**:
   ```bash
   # Verify .env.local has correct API key
   GOOGLE_GEMINI_API_KEY=your_actual_api_key_here
   ```

2. **Test API endpoint directly**:
   ```bash
   curl -X POST http://localhost:3000/api/admin/analytics/chatbot \
     -H "Content-Type: application/json" \
     -H "Cookie: your-session-cookie" \
     -d '{"message": "How many members do we have?", "conversationHistory": []}'
   ```

3. **Check browser console**:
   - Open Developer Tools (F12)
   - Look for API response logs
   - Check for error messages

4. **Verify database connection**:
   ```bash
   # Test database connectivity
   npx prisma db pull
   ```

#### AI Service Not Configured Error

**Problem**: "AI service not configured" error message

**Solutions**:
1. **Get API key from Google AI Studio**:
   - Visit https://aistudio.google.com/app/apikey
   - Create new API key
   - Add to environment variables

2. **Restart development server**:
   ```bash
   npm run dev
   ```

3. **Check API key format**:
   - Should start with "AIza"
   - No extra spaces or characters

### Build and Deployment Issues

#### Build Freezing on Oracle VPS

**Problem**: `npm run build` freezes due to memory limitations

**Solution**: Build locally and upload:
```bash
# On local Windows machine
npm run build

# Upload to VPS via SCP
scp -r .next user@server:/path/to/project/
```

#### Environment Variable Issues

**Problem**: Environment variables not loading correctly

**Solutions**:
1. **Use .env.local for both development and production**:
   ```bash
   # Install dotenv for Prisma
   npm install dotenv-cli
   ```

2. **Windows PowerShell compatibility**:
   ```powershell
   # Instead of SKIP_ENV_VALIDATION=true npm run build
   $env:SKIP_ENV_VALIDATION="true"; npm run build
   ```

### Performance Issues

#### Slow Page Load Times

**Problem**: Pages loading slowly, especially with large datasets

**Solutions**:

1. **Implement pagination**:
   ```typescript
   const { data, totalCount } = await getPaginatedData({
     page: 1,
     limit: 10,
   });
   ```

2. **Use database indexes**:
   ```sql
   CREATE INDEX idx_members_name ON members(first_name, last_name);
   ```

3. **Optimise queries**:
   ```typescript
   // Select only needed fields
   const members = await prisma.member.findMany({
     select: {
       id: true,
       firstName: true,
       lastName: true,
     },
   });
   ```

#### Memory Leaks

**Problem**: Application memory usage increasing over time

**Solutions**:
1. **Check for unclosed database connections**:
   ```typescript
   // Always use try/finally for cleanup
   try {
     const result = await prisma.member.findMany();
   } finally {
     // Cleanup if needed
   }
   ```

2. **Monitor connection pool**:
   ```typescript
   // Check pool status
   const poolStatus = await prisma.$queryRaw`
     SELECT count(*) FROM pg_stat_activity;
   `;
   ```

### UI/UX Issues

#### DatePicker Non-Interactive in Dialogs

**Problem**: DatePicker components become non-interactive when used inside Dialog/Modal components

**Symptoms**:
- DatePicker opens correctly when clicked
- Calendar dates are not clickable
- "Today" and "Done" buttons don't respond
- Issue occurs in edit member/household dialogs and admin settings
- Works fine in census forms (not in dialogs)

**Root Cause**: Radix UI Dialog sets `pointer-events: none` on `document.body` when modals are open, preventing interaction with Popover components rendered in portals

**Solution**: Two-pronged approach implemented across all datepicker components:

1. **Add `modal={false}` to Popover components**:
   ```typescript
   <Popover modal={false} onOpenChange={setIsOpen} open={isOpen}>
   ```

2. **Force pointer-events override when popover opens**:
   ```typescript
   // Fix for popover inside dialog - force pointer events when popover is open
   React.useEffect(() => {
     if (isOpen) {
       // Force pointer-events to be enabled when popover opens
       const originalPointerEvents = document.body.style.pointerEvents;
       document.body.style.pointerEvents = 'auto';

       // Cleanup function to restore original pointer events
       return () => {
         document.body.style.pointerEvents = originalPointerEvents;
       };
     }
   }, [isOpen]);
   ```

**Components Fixed**:
- ✅ `DatePicker` component (`src/components/ui/date-picker.tsx`)
- ✅ `CustomDateTimePicker` component (`src/components/ui/custom-datetime-picker.tsx`)
- ✅ Member sacraments date pickers (`src/components/admin/members/editable-member-sacraments.tsx`)

**Prevention**: This fix is now built into all datepicker components and will automatically work for any new datepickers added to dialogs.

#### Form Validation Not Working

**Problem**: Form validation errors not displaying

**Solutions**:
1. **Check Zod schema configuration**:
   ```typescript
   const schema = z.object({
     field: z.string().min(1, { error: 'Field is required' }),
   });
   ```

2. **Verify React Hook Form integration**:
   ```typescript
   const form = useForm({
     resolver: zodResolver(schema),
   });
   ```

#### Toast Notifications Not Appearing

**Problem**: Success/error messages not showing

**Solutions**:

1. **Use the centralized alert system** (Recommended):
   ```typescript
   import { useMessage } from '@/hooks/useMessage';

   export function MyComponent() {
     const { showSuccess, showError, showWarning, showInfo } = useMessage();

     const handleAction = () => {
       showSuccess('operationCompleted'); // Automatic translation
     };
   }
   ```

2. **Check Sonner provider setup**:
   ```typescript
   import { Toaster } from 'sonner';

   export default function RootLayout({ children }) {
     return (
       <html>
         <body>
           {children}
           <Toaster />
         </body>
       </html>
     );
   }
   ```

3. **Verify AlertContext provider**:
   ```typescript
   import { AlertProvider } from '@/contexts/AlertContext';

   export default function Layout({ children }) {
     return (
       <AlertProvider>
         {children}
       </AlertProvider>
     );
   }
   ```

4. **Check message key mappings**:
   ```typescript
   // Ensure message keys exist in mapping files
   // src/lib/messages/success-messages.ts
   // src/lib/errors/auth-errors.ts, etc.
   ```

#### Centralized Alert System Issues

**Problem**: Messages not translating correctly

**Solutions**:

1. **Verify translation structure and patterns**:
   ```bash
   # Check translation file structure
   node test/translation-sort-verify.js

   # Check for deprecated patterns
   grep -r "\.format()" app/api/ --include="*.ts"
   grep -r "{ message:" src/lib/validation/ --include="*.ts"
   ```

2. **Check auth context detection**:
   ```typescript
   // useMessage hook automatically detects context
   // Admin: /admin/* paths use authErrorKeys
   // Census: /census/* paths use censusErrorKeys
   ```

3. **Verify message key exists**:
   ```typescript
   // Check if key exists in appropriate mapping file
   import { successMessageKeys } from '@/lib/messages/success-messages';
   console.log(successMessageKeys['yourMessageKey']);
   ```

4. **Check server-side message setup**:
   ```typescript
   import { setServerMessage } from '@/lib/utils/server-messages';

   // In API route
   await setServerMessage('success', 'operationCompleted', 'census');
   ```

## Debugging Tools

### Development Environment

#### Enable Debug Logging
```bash
# Add to .env.local
NODE_ENV=development
DEBUG=true
```

#### Database Query Logging
```typescript
const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
});
```

### Production Environment

#### Check Application Logs
```bash
# PM2 logs
pm2 logs wsccc-census

# System logs
journalctl -u your-service-name -f
```

#### Monitor Database Performance
```sql
-- PostgreSQL: Check slow queries
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;
```

## Emergency Procedures

### Database Recovery

1. **Stop application**:
   ```bash
   pm2 stop wsccc-census
   ```

2. **Restore from backup**:
   ```bash
   psql -d database_name < backup_file.sql
   ```

3. **Restart application**:
   ```bash
   pm2 start wsccc-census
   ```

### Rate Limit Reset

```sql
-- Clear all rate limits
DELETE FROM auth_rate_limits;

-- Clear specific user rate limit
DELETE FROM auth_rate_limits 
WHERE session_token = 'hashed_token';
```

### Cache Clear

```typescript
// Clear application cache
await appCache.clear();

// Clear specific cache prefix
await appCache.clearByPrefix('qr-code:');
```

This troubleshooting guide covers the most common issues encountered in the WSCCC Census System and provides practical solutions for quick resolution.
